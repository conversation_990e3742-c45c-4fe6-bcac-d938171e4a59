<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 基本操作区域 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="基本操作"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="扇区:" />

            <EditText
                android:id="@+id/edit_sector_1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="0"
                android:inputType="number" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="密钥A:" />

            <EditText
                android:id="@+id/edit_keyA_1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="FFFFFFFFFFFF"
                android:inputType="textCapCharacters" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="密钥B:" />

            <EditText
                android:id="@+id/edit_keyB_1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="FFFFFFFFFFFF"
                android:inputType="textCapCharacters" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="块0:" />

            <EditText
                android:id="@+id/edit_block_0"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="数据块0"
                android:inputType="textCapCharacters" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="块1:" />

            <EditText
                android:id="@+id/edit_block_1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="数据块1"
                android:inputType="textCapCharacters" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="块2:" />

            <EditText
                android:id="@+id/edit_block_2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="数据块2"
                android:inputType="textCapCharacters" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/mb_read"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="读取" />

            <Button
                android:id="@+id/mb_write"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="写入" />
        </LinearLayout>

        <!-- 钱包操作区域 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="钱包操作"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="扇区:" />

            <EditText
                android:id="@+id/edit_sector_2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="1"
                android:inputType="number" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="块:" />

            <EditText
                android:id="@+id/edit_block"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="0"
                android:inputType="number" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="密钥A:" />

            <EditText
                android:id="@+id/edit_keyA_2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="FFFFFFFFFFFF"
                android:inputType="textCapCharacters" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="密钥B:" />

            <EditText
                android:id="@+id/edit_keyB_2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="FFFFFFFFFFFF"
                android:inputType="textCapCharacters" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="金额:" />

            <EditText
                android:id="@+id/edit_cost"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="10"
                android:inputType="number" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_balance"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="余额: 0"
            android:textSize="16sp"
            android:layout_marginBottom="16dp"
            android:padding="8dp"
            android:background="#f0f0f0" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/mb_init"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:text="初始化" />

            <Button
                android:id="@+id/mb_balance"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:text="余额" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/mb_add"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:text="增加" />

            <Button
                android:id="@+id/mb_reduce"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:text="减少" />
        </LinearLayout>

        <!-- 恢复操作区域 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="恢复操作"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="扇区:" />

            <EditText
                android:id="@+id/edit_sector_3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="1"
                android:inputType="number" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="块:" />

            <EditText
                android:id="@+id/edit_block_3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="0"
                android:inputType="number" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="密钥A:" />

            <EditText
                android:id="@+id/edit_keyA_3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="FFFFFFFFFFFF"
                android:inputType="textCapCharacters" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="密钥B:" />

            <EditText
                android:id="@+id/edit_keyB_3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="FFFFFFFFFFFF"
                android:inputType="textCapCharacters" />
        </LinearLayout>

        <Button
            android:id="@+id/mb_restore"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="恢复" />

    </LinearLayout>
</ScrollView>
