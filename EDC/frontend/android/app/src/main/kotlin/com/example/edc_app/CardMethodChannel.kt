package com.example.edc_app

import android.os.Bundle
import android.os.RemoteException
import android.util.Log
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import java.util.*

/**
 * 卡模块方法通道处理器
 * 负责处理Flutter端与原生卡模块的通信
 */
class CardMethodChannel(private val cardModule: CardModule) : MethodCallHandler {
    companion object {
        private const val TAG = "CardMethodChannel"
    }

    /**
     * 处理Flutter端的方法调用
     */
    override fun onMethodCall(call: MethodCall, result: Result) {
        Log.d(TAG, "Method called: ${call.method}")

        // --- 在调用具体方法前检查SDK连接状态 ---
        val isConnected = EdcApplication.instance.isConnectPaySDK()
        Log.d(TAG, "SDK connection status: $isConnected")

        if (!isConnected) {
            Log.e(TAG, "SDK not connected when calling ${call.method}")
            result.error("SDK_NOT_CONNECTED", "商米支付SDK未连接", null)
            return
        }
        // --------------------------------------

        Log.d(TAG, "SDK is connected, proceeding with method: ${call.method}")

        when (call.method) {
            // NFC卡相关
            "startCheckNFCCard" -> {
                Log.d(TAG, "Calling cardModule.startCheckNFCCard")
                cardModule.startCheckNFCCard(result)
            }
            "stopCheckNFCCard" -> {
                cardModule.stopCheckNFCCard(result)
            }
            "checkM1Card" -> {
                cardModule.checkM1Card(result)
            }
            
            // M1卡相关
            "m1Auth" -> {
                val sector = call.argument<Int>("sector") ?: 0
                val keyType = call.argument<Int>("keyType") ?: 0
                val key = call.argument<String>("key") ?: "FFFFFFFFFFFF"
                cardModule.m1Auth(sector, keyType, key, result)
            }
            "m1ReadBlock" -> {
                val sector = call.argument<Int>("sector") ?: 0
                val block = call.argument<Int>("block") ?: 0
                cardModule.m1ReadBlock(sector, block, result)
            }
            "m1WriteBlock" -> {
                val sector = call.argument<Int>("sector") ?: 0
                val block = call.argument<Int>("block") ?: 0
                val data = call.argument<String>("data") ?: ""
                cardModule.m1WriteBlock(sector, block, data, result)
            }
            "m1InitWallet" -> {
                val sector = call.argument<Int>("sector") ?: 1
                val block = call.argument<Int>("block") ?: 0
                cardModule.m1InitWallet(sector, block, result)
            }
            "m1GetBalance" -> {
                val sector = call.argument<Int>("sector") ?: 1
                val block = call.argument<Int>("block") ?: 0
                cardModule.m1GetBalance(sector, block, result)
            }
            "m1IncreaseValue" -> {
                val sector = call.argument<Int>("sector") ?: 1
                val block = call.argument<Int>("block") ?: 0
                val amount = call.argument<Int>("amount") ?: 0
                cardModule.m1IncreaseValue(sector, block, amount, result)
            }
            "m1DecreaseValue" -> {
                val sector = call.argument<Int>("sector") ?: 1
                val block = call.argument<Int>("block") ?: 0
                val amount = call.argument<Int>("amount") ?: 0
                cardModule.m1DecreaseValue(sector, block, amount, result)
            }
            "m1Restore" -> {
                val sector = call.argument<Int>("sector") ?: 1
                val block = call.argument<Int>("block") ?: 0
                cardModule.m1Restore(sector, block, result)
            }
            "m1ReadAllSector" -> {
                val sector = call.argument<Int>("sector") ?: 0
                cardModule.m1ReadAllSector(sector, result)
            }
            "m1WriteAllSector" -> {
                val sector = call.argument<Int>("sector") ?: 0
                val blockData = call.argument<Map<String, String>>("blockData") ?: emptyMap()
                cardModule.m1WriteAllSector(sector, blockData, result)
            }
            else -> {
                result.notImplemented()
            }
        }
    }
} 