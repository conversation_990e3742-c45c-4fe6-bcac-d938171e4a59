package com.example.edc_app

import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.RemoteException
import android.util.Log
import io.flutter.plugin.common.MethodChannel
import sunmi.paylib.SunmiPayKernel
import com.sunmi.pay.hardware.aidlv2.readcard.ReadCardOptV2
import com.sunmi.pay.hardware.aidlv2.AidlErrorCodeV2
import com.sunmi.pay.hardware.aidlv2.readcard.CheckCardCallbackV2
import com.sunmi.pay.hardware.aidlv2.AidlConstantsV2
import java.util.*
import com.example.edc_app.EdcApplication

/**
 * 卡模块实现类
 * 负责调用商米SDK实现卡模块功能
 */
class CardModule(private val context: Context) {
    companion object {
        private const val TAG = "CardModule"

        // 卡类型常量定义 (根据商米SDK文档)
        private const val CARD_TYPE_MAGNETIC = 1   // 1<<0 磁条卡
        private const val CARD_TYPE_IC = 2         // 1<<1 IC卡
        private const val CARD_TYPE_NFC = 4        // 1<<2 NFC卡
        private const val CARD_TYPE_MIFARE = 8     // 1<<3 MIFARE卡
        private const val CARD_TYPE_FELICA = 32    // 1<<5 FeliCa卡
        private const val CARD_TYPE_MIFARE_PLUS = 128    // 1<<7 MIFARE Plus
        private const val CARD_TYPE_MIFARE_DESFIRE = 256 // 1<<8 MIFARE DESFire
    }

    private val mainHandler = Handler(Looper.getMainLooper())

    private var checkCardCallback: CheckCardCallbackV2? = null
    private var resultCallback: MethodChannel.Result? = null
    private var channel: MethodChannel? = null

    // 性能监控 (参考M1Activity的性能监控模式)
    private val operationTimes = mutableMapOf<String, Long>()

    /**
     * 记录操作开始时间 (参考M1Activity.addStartTimeWithClear)
     */
    private fun addStartTimeWithClear(operation: String) {
        operationTimes[operation] = System.currentTimeMillis()
        Log.d(TAG, "⏱️ Started: $operation")
    }

    /**
     * 记录操作结束时间并显示耗时 (参考M1Activity.addEndTime + showSpendTime)
     */
    private fun addEndTimeAndShowSpent(operation: String): Long {
        val startTime = operationTimes[operation]
        val endTime = System.currentTimeMillis()
        val duration = if (startTime != null) endTime - startTime else 0
        Log.d(TAG, "⏱️ Completed: $operation in ${duration}ms")
        operationTimes.remove(operation)
        return duration
    }
    
    /**
     * 设置 MethodChannel (如果需要回调 Flutter)
     */
    fun setChannel(channel: MethodChannel) {
        this.channel = channel
        Log.d(TAG, "MethodChannel set for CardModule")
    }
    
    /**
     * 开始检测NFC卡
     */
    fun startCheckNFCCard(result: MethodChannel.Result) {
        Log.d(TAG, "startCheckNFCCard called.")

        // 验证SDK状态和常量
        if (!validateSDKAndConstants()) {
            result.error("SDK_VALIDATION_FAILED", "SDK验证失败", null)
            return
        }

        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            Log.e(TAG, "readCardOptV2 is null when trying to start NFC check. SDK might not be connected.")
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接或CardModule未正确初始化", null)
            return
        }

        try {
            Log.i(TAG, "Attempting to start NFC card check using $currentReadCardOptV2")

            // 检查 ReadCardOptV2 实例状态
            Log.d(TAG, "ReadCardOptV2 instance: $currentReadCardOptV2")
            Log.d(TAG, "ReadCardOptV2 class: ${currentReadCardOptV2.javaClass.name}")

            resultCallback = result
            
            checkCardCallback = object : CheckCardCallbackV2.Stub() {
                @Throws(RemoteException::class)
                override fun findMagCard(info: Bundle) {
                    Log.i(TAG, "🔍 findMagCard callback triggered: $info")
                }

                @Throws(RemoteException::class)
                override fun findICCardEx(info: Bundle) {
                    Log.i(TAG, "🔍 findICCardEx callback triggered: $info")
                }

                @Throws(RemoteException::class)
                override fun findRFCardEx(info: Bundle) {
                    Log.i(TAG, "🔍 findRFCardEx callback triggered from Binder thread: $info")
                    val cardType = info.getInt("cardType")
                    val uuid = info.getString("uuid") ?: ""
                    val ats = info.getString("ats") ?: ""
                    val cardCategory = info.getInt("cardCategory")
                    
                    val cardInfo = HashMap<String, Any>()
                    cardInfo["cardType"] = getCardTypeName(cardType)
                    cardInfo["cardCategory"] = getCardCategoryName(cardCategory)
                    cardInfo["uuid"] = uuid
                    cardInfo["ats"] = ats
                    
                    mainHandler.post {
                        Log.d(TAG, "Invoking onCardDetected on main thread")
                        channel?.invokeMethod("onCardDetected", cardInfo)
                    }
                }
                
                @Throws(RemoteException::class)
                override fun onErrorEx(info: Bundle) {
                    Log.i(TAG, "🔍 onErrorEx callback triggered from Binder thread: $info")
                    val code = info.getInt("code")
                    val message = info.getString("message") ?: "未知错误"
                    val cardType = info.getInt("cardType", -1)

                    Log.e(TAG, "Card check error - Code: $code, Message: $message, CardType: $cardType")

                    // 详细的错误分析
                    val detailedMessage = when (code) {
                        -20003 -> "参数错误: 检查卡类型参数是否正确"
                        -2001 -> "没有检测到卡片"
                        -2002 -> "检测到多张卡片"
                        -30003 -> "NFC检测失败"
                        -30004 -> "IC卡检测失败"
                        -30005 -> "读卡超时"
                        else -> message
                    }

                    val errorInfo = HashMap<String, Any>()
                    errorInfo["code"] = code
                    errorInfo["message"] = detailedMessage
                    errorInfo["originalMessage"] = message
                    if (cardType != -1) {
                        errorInfo["cardType"] = getCardTypeName(cardType)
                    }

                    mainHandler.post {
                        Log.d(TAG, "Invoking onCardError on main thread with detailed info")
                        channel?.invokeMethod("onCardError", errorInfo)
                    }
                }

                override fun findICCard(atr: String?) {
                    Log.i(TAG, "🔍 findICCard (legacy) callback triggered: $atr")
                }
                override fun findRFCard(uuid: String?) {
                    Log.i(TAG, "🔍 findRFCard (legacy) callback triggered: $uuid")
                }
                override fun onError(code: Int, message: String?) {
                    Log.i(TAG, "🔍 onError (legacy) callback triggered: code=$code, message=$message")
                }
            }
            
            // 使用正确的卡类型常量
            // 检测所有类型的卡：磁条卡 + IC卡 + NFC卡
            val cardType = CARD_TYPE_MAGNETIC or CARD_TYPE_IC or CARD_TYPE_NFC
            val timeout = 60   // 60秒超时

            Log.d(TAG, "Calling checkCard with cardType: $cardType (MAGNETIC|IC|NFC), timeout: ${timeout}s")
            Log.d(TAG, "CardType values - MAGNETIC: $CARD_TYPE_MAGNETIC, IC: $CARD_TYPE_IC, NFC: $CARD_TYPE_NFC")

            try {
                // 使用标准的checkCard方法而不是checkCardEx
                currentReadCardOptV2.checkCard(cardType, checkCardCallback, timeout)
                Log.d(TAG, "checkCard call completed successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Exception during checkCard call: ${e.message}", e)
                throw e
            }

            // 添加一个定时器来检查是否有响应
            mainHandler.postDelayed({
                Log.d(TAG, "⏰ 10秒后检查：NFC检测状态...")
                Log.d(TAG, "   - checkCardCallback: $checkCardCallback")
                Log.d(TAG, "   - resultCallback: $resultCallback")
                Log.d(TAG, "   - 如果没有检测到卡片，请尝试将NFC卡靠近设备")

                // 发送状态更新到Flutter
                val statusInfo = HashMap<String, Any>()
                statusInfo["status"] = "waiting"
                statusInfo["message"] = "正在等待卡片检测，请将卡片靠近设备..."
                statusInfo["timeout"] = 60

                mainHandler.post {
                    channel?.invokeMethod("onCardStatus", statusInfo)
                }
            }, 10000)

            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error starting NFC card check: ${e.message}")
            result.error("CHECK_CARD_ERROR", "检测NFC卡出错: ${e.message}", null)
        }
    }
    
    /**
     * 停止检测NFC卡 (参考M1Activity.cancelCheckCard)
     */
    fun stopCheckNFCCard(result: MethodChannel.Result) {
        Log.d(TAG, "Stopping NFC card check")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            Log.e(TAG, "readCardOptV2 is null when trying to stop NFC check.")
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null)
            return
        }
        try {
            // 参考M1Activity的cancelCheckCard方法，先关闭MIFARE卡，再取消检测
            currentReadCardOptV2.cardOff(CARD_TYPE_MIFARE)
            currentReadCardOptV2.cancelCheckCard()

            // 清理回调引用
            checkCardCallback = null
            resultCallback = null

            Log.d(TAG, "NFC card check stopped successfully")
            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping NFC card check: ${e.message}")
            result.error("CANCEL_CHECK_CARD_ERROR", "停止检测NFC卡出错: ${e.message}", null)
        }
    }

    /**
     * 检测M1卡 (参考M1Activity.checkCard)
     */
    fun checkM1Card(result: MethodChannel.Result) {
        Log.d(TAG, "Starting M1 card check")
        val startTime = System.currentTimeMillis()

        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            Log.e(TAG, "readCardOptV2 is null when trying to check M1 card.")
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null)
            return
        }

        try {
            // 创建M1卡检测回调
            val m1CheckCallback = object : CheckCardCallbackV2() {
                override fun findMagCard(bundle: Bundle?) {
                    val endTime = System.currentTimeMillis()
                    Log.d(TAG, "findMagCard detected in ${endTime - startTime}ms")
                    val response = HashMap<String, Any>()
                    response["success"] = true
                    response["cardType"] = "MAGNETIC"
                    response["duration"] = endTime - startTime
                    result.success(response)
                }

                override fun findICCardEx(info: Bundle?) {
                    val endTime = System.currentTimeMillis()
                    Log.d(TAG, "findICCardEx detected in ${endTime - startTime}ms: ${info?.toString()}")
                    val response = HashMap<String, Any>()
                    response["success"] = true
                    response["cardType"] = "MIFARE"
                    response["cardInfo"] = info?.toString() ?: ""
                    response["duration"] = endTime - startTime
                    result.success(response)
                }

                override fun onError(code: Int, message: String?) {
                    val endTime = System.currentTimeMillis()
                    Log.e(TAG, "M1 card check error in ${endTime - startTime}ms: $code - $message")
                    val response = HashMap<String, Any>()
                    response["success"] = false
                    response["message"] = "检测M1卡失败: $message"
                    response["code"] = code
                    response["duration"] = endTime - startTime
                    result.success(response)
                }
            }

            // 使用MIFARE卡类型进行检测
            Log.d(TAG, "Calling checkCard with MIFARE card type")
            currentReadCardOptV2.checkCard(CARD_TYPE_MIFARE, m1CheckCallback, 60)

        } catch (e: Exception) {
            val endTime = System.currentTimeMillis()
            Log.e(TAG, "Error starting M1 card check: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "启动M1卡检测异常: ${e.message}"
            response["duration"] = endTime - startTime
            result.success(response)
        }
    }
    
    /**
     * M1卡认证
     */
    fun m1Auth(sector: Int, keyType: Int, key: String, result: MethodChannel.Result) {
        Log.d(TAG, "M1 auth - sector: $sector, keyType: $keyType, key: $key")
        addStartTimeWithClear("mifareAuth()")

        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            Log.e(TAG, "readCardOptV2 is null when trying M1 auth.")
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null)
            return
        }

        // 参数验证
        val validationError = validateM1AuthParams(sector, keyType, key)
        if (validationError != null) {
            Log.e(TAG, "Parameter validation failed: $validationError")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = validationError
            result.success(response)
            return
        }

        try {
            val keyBytes = hexStringToByteArray(key)
            if (keyBytes.size != 6) {
                Log.e(TAG, "Key bytes length is ${keyBytes.size}, expected 6")
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = "密钥长度错误: 必须是6字节"
                result.success(response)
                return
            }

            val block = sector * 4
            Log.d(TAG, "Calling mifareAuth with keyType: $keyType, block: $block, keyBytes: ${bytesToHexString(keyBytes)}")
            val code = currentReadCardOptV2.mifareAuth(keyType, block, keyBytes) ?: -1
            val duration = addEndTimeAndShowSpent("mifareAuth()")

            if (code == 0) {
                val response = HashMap<String, Any>()
                response["success"] = true
                response["duration"] = duration
                result.success(response)
            } else {
                val errorMsg = getM1ErrorMessage(code, "认证")
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                response["duration"] = duration
                result.success(response)
            }
        } catch (e: IllegalArgumentException) {
            val duration = addEndTimeAndShowSpent("mifareAuth()")
            Log.e(TAG, "Parameter validation error in M1 auth: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "参数错误: ${e.message}"
            response["duration"] = duration
            result.success(response)
        } catch (e: Exception) {
            val duration = addEndTimeAndShowSpent("mifareAuth()")
            Log.e(TAG, "Error in M1 auth: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "认证异常: ${e.message}"
            response["duration"] = duration
            result.success(response)
        }
    }
    
    /**
     * 读取M1卡数据块
     */
    fun m1ReadBlock(sector: Int, block: Int, result: MethodChannel.Result) {
        Log.d(TAG, "M1 read block - sector: $sector, block: $block")
        val startTime = System.currentTimeMillis()

        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }

        // 参数验证
        val validationError = validateBlockParams(sector, block)
        if (validationError != null) {
            Log.e(TAG, "Parameter validation failed: $validationError")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = validationError
            result.success(response)
            return
        }

        try {
            val blockIndex = sector * 4 + block
            val blockData = ByteArray(16)
            Log.d(TAG, "Calling mifareReadBlock with blockIndex: $blockIndex")
            val code = currentReadCardOptV2.mifareReadBlock(blockIndex, blockData) ?: -1
            val endTime = System.currentTimeMillis()
            Log.d(TAG, "mifareReadBlock completed in ${endTime - startTime}ms with result: $code")

            if (code >= 0 && code <= 16) {
                val hexStr = bytesToHexString(blockData.copyOf(code))
                val response = HashMap<String, Any>()
                response["success"] = true
                val data = HashMap<String, Any>()
                data["block$block"] = hexStr
                response["data"] = data
                response["duration"] = endTime - startTime
                result.success(response)
            } else {
                val errorMsg = getM1ErrorMessage(code, "读取")
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                response["duration"] = endTime - startTime
                result.success(response)
            }
        } catch (e: Exception) {
            val endTime = System.currentTimeMillis()
            Log.e(TAG, "Error in M1 read block: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "读取异常: ${e.message}"
            response["duration"] = endTime - startTime
            result.success(response)
        }
    }
    
    /**
     * 写入M1卡数据块
     */
    fun m1WriteBlock(sector: Int, block: Int, data: String, result: MethodChannel.Result) {
        Log.d(TAG, "M1 write block - sector: $sector, block: $block, data: $data")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }

        // 参数验证
        if (sector < 0 || sector > 15) {
            Log.e(TAG, "Invalid sector: $sector. Must be 0-15")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的扇区号: $sector，必须在0-15之间"
            result.success(response)
            return
        }

        if (block < 0 || block > 3) {
            Log.e(TAG, "Invalid block: $block. Must be 0-3")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的块号: $block，必须在0-3之间"
            result.success(response)
            return
        }

        // 检查是否尝试写入控制块（每个扇区的第3块）
        if (block == 3) {
            Log.w(TAG, "Warning: Attempting to write to control block (block 3) in sector $sector")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "不能写入控制块: 扇区 $sector 的块 3 是控制块"
            result.success(response)
            return
        }

        if (data.replace(" ", "").length != 32) {
            Log.e(TAG, "Invalid data length: ${data.length}. Must be 32 hex characters (16 bytes)")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的数据长度: 必须是32位16进制字符(16字节)"
            result.success(response)
            return
        }

        try {
            val blockIndex = sector * 4 + block
            if (blockIndex < 0 || blockIndex > 63) {
                Log.e(TAG, "Invalid blockIndex: $blockIndex. Must be 0-63")
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = "无效的绝对块号: $blockIndex，必须在0-63之间"
                result.success(response)
                return
            }

            val blockData = hexStringToByteArray(data)
            if (blockData.size != 16) {
                Log.e(TAG, "Block data size is ${blockData.size}, expected 16")
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = "数据长度错误: 必须是16字节"
                result.success(response)
                return
            }

            Log.d(TAG, "Calling mifareWriteBlock with blockIndex: $blockIndex, data: ${bytesToHexString(blockData)}")
            val code = currentReadCardOptV2.mifareWriteBlock(blockIndex, blockData) ?: -1
            if (code == 0) {
                val response = HashMap<String, Any>()
                response["success"] = true
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    -20003 -> "参数错误: 检查扇区号、块号或数据格式"
                    -2000 -> "卡参数错误"
                    -2001 -> "没有卡片"
                    -30001 -> "写卡失败(未知原因)"
                    else -> try {
                        "写入失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                    } catch (e: Exception) {
                        "写入失败: 未知错误代码 $code"
                    }
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: IllegalArgumentException) {
            Log.e(TAG, "Parameter validation error in M1 write block: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "参数错误: ${e.message}"
            result.success(response)
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 write block: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "写入异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 初始化M1卡钱包
     */
    fun m1InitWallet(sector: Int, block: Int, result: MethodChannel.Result) {
        Log.d(TAG, "M1 init wallet - sector: $sector, block: $block")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
             result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }

        // 参数验证
        if (sector < 0 || sector > 15) {
            Log.e(TAG, "Invalid sector: $sector. Must be 0-15")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的扇区号: $sector，必须在0-15之间"
            result.success(response)
            return
        }

        if (block < 0 || block > 2) {
            Log.e(TAG, "Invalid block for wallet: $block. Must be 0-2 (not control block)")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的钱包块号: $block，钱包只能使用0-2块"
            result.success(response)
            return
        }

        try {
            val blockIndex = sector * 4 + block
            if (blockIndex < 0 || blockIndex > 63) {
                Log.e(TAG, "Invalid blockIndex: $blockIndex. Must be 0-63")
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = "无效的绝对块号: $blockIndex，必须在0-63之间"
                result.success(response)
                return
            }

            val initData = getInitWalletData(blockIndex)
            Log.d(TAG, "Calling mifareWriteBlock for wallet init with blockIndex: $blockIndex, data: ${bytesToHexString(initData)}")
            val code = currentReadCardOptV2.mifareWriteBlock(blockIndex, initData) ?: -1
            if (code == 0) {
                val response = HashMap<String, Any>()
                response["success"] = true
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    -20003 -> "参数错误: 检查扇区号和块号"
                    -2000 -> "卡参数错误"
                    -2001 -> "没有卡片"
                    else -> try {
                        "初始化钱包失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                    } catch (e: Exception) {
                        "初始化钱包失败: 未知错误代码 $code"
                    }
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 init wallet: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "初始化钱包异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 获取M1卡钱包余额 (参考M1Activity.getBalanceWallet)
     */
    fun m1GetBalance(sector: Int, block: Int, result: MethodChannel.Result) {
         Log.d(TAG, "M1 get balance - sector: $sector, block: $block")
        val startTime = System.currentTimeMillis()

        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
             result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }

        // 参数验证
        val validationError = validateWalletParams(sector, block)
        if (validationError != null) {
            Log.e(TAG, "Parameter validation failed: $validationError")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = validationError
            result.success(response)
            return
        }

        try {
            val blockIndex = sector * 4 + block
            val blockData = ByteArray(16)
            Log.d(TAG, "Calling mifareReadBlock for balance with blockIndex: $blockIndex")
            val code = currentReadCardOptV2.mifareReadBlock(blockIndex, blockData) ?: -1
            val endTime = System.currentTimeMillis()
            Log.d(TAG, "mifareReadBlock for balance completed in ${endTime - startTime}ms with result: $code")

            if (code >= 0 && code <= 16) {
                val hexStr = bytesToHexString(blockData.copyOf(code))
                Log.d(TAG, "get wallet balance outData: $hexStr")

                // 使用与M1Activity相同的方式读取余额 (小端格式)
                val balance = getInt32FromBytes(blockData, 0, true)
                Log.d(TAG, "Wallet balance: $balance")

                val response = HashMap<String, Any>()
                response["success"] = true
                response["balance"] = balance
                response["hexData"] = hexStr
                response["duration"] = endTime - startTime
                result.success(response)
            } else {
                val errorMsg = getM1ErrorMessage(code, "获取余额")
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                response["duration"] = endTime - startTime
                result.success(response)
            }
        } catch (e: Exception) {
            val endTime = System.currentTimeMillis()
            Log.e(TAG, "Error in M1 get balance: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "获取余额异常: ${e.message}"
            response["duration"] = endTime - startTime
            result.success(response)
        }
    }
    
    /**
     * 增加M1卡钱包金额
     */
    fun m1IncreaseValue(sector: Int, block: Int, amount: Int, result: MethodChannel.Result) {
         Log.d(TAG, "M1 increase value - sector: $sector, block: $block, amount: $amount")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
             result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }

        // 参数验证
        val validationError = validateWalletParams(sector, block)
        if (validationError != null) {
            Log.e(TAG, "Parameter validation failed: $validationError")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = validationError
            result.success(response)
            return
        }

        if (amount <= 0) {
            Log.e(TAG, "Invalid amount: $amount. Must be positive")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的金额: $amount，必须大于0"
            result.success(response)
            return
        }

        try {
            val blockIndex = sector * 4 + block
            val amountData = int32ToBytes(amount, true)
            Log.d(TAG, "Calling mifareIncValue with blockIndex: $blockIndex, amount: $amount")
            val code = currentReadCardOptV2.mifareIncValue(blockIndex, amountData) ?: -1
            if (code == 0) {
                m1GetBalance(sector, block, result)
                return
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    -20003 -> "参数错误: 检查扇区号、块号或金额"
                    -2000 -> "卡参数错误"
                    -2001 -> "没有卡片"
                    else -> try {
                        "增加金额失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                    } catch (e: Exception) {
                        "增加金额失败: 未知错误代码 $code"
                    }
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 increase value: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "增加金额异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 减少M1卡钱包金额
     */
    fun m1DecreaseValue(sector: Int, block: Int, amount: Int, result: MethodChannel.Result) {
         Log.d(TAG, "M1 decrease value - sector: $sector, block: $block, amount: $amount")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }

        // 参数验证
        val validationError = validateWalletParams(sector, block)
        if (validationError != null) {
            Log.e(TAG, "Parameter validation failed: $validationError")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = validationError
            result.success(response)
            return
        }

        if (amount <= 0) {
            Log.e(TAG, "Invalid amount: $amount. Must be positive")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的金额: $amount，必须大于0"
            result.success(response)
            return
        }

        try {
            val blockIndex = sector * 4 + block
            val amountData = int32ToBytes(amount, true)
            Log.d(TAG, "Calling mifareDecValue with blockIndex: $blockIndex, amount: $amount")
            val code = currentReadCardOptV2.mifareDecValue(blockIndex, amountData) ?: -1
            if (code == 0) {
                m1GetBalance(sector, block, result)
                return
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    -20003 -> "参数错误: 检查扇区号、块号或金额"
                    -2000 -> "卡参数错误"
                    -2001 -> "没有卡片"
                    else -> try {
                        "减少金额失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                    } catch (e: Exception) {
                        "减少金额失败: 未知错误代码 $code"
                    }
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 decrease value: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "减少金额异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * M1卡恢复操作
     */
    fun m1Restore(sector: Int, block: Int, result: MethodChannel.Result) {
         Log.d(TAG, "M1 restore - sector: $sector, block: $block")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
             result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }

        // 参数验证
        val validationError = validateWalletParams(sector, block)
        if (validationError != null) {
            Log.e(TAG, "Parameter validation failed: $validationError")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = validationError
            result.success(response)
            return
        }

        try {
            val blockIndex = sector * 4 + block
            Log.d(TAG, "Calling mifareRestore with blockIndex: $blockIndex")
            val code = currentReadCardOptV2.mifareRestore(blockIndex) ?: -1
            if (code == 0) {
                val response = HashMap<String, Any>()
                response["success"] = true
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    -20003 -> "参数错误: 检查扇区号和块号"
                    -2000 -> "卡参数错误"
                    -2001 -> "没有卡片"
                    else -> try {
                        "恢复操作失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                    } catch (e: Exception) {
                        "恢复操作失败: 未知错误代码 $code"
                    }
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 restore: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "恢复操作异常: ${e.message}"
            result.success(response)
        }
    }

    /**
     * 批量读取M1卡扇区所有数据块 (参考M1Activity.readAllSector)
     */
    fun m1ReadAllSector(sector: Int, result: MethodChannel.Result) {
        Log.d(TAG, "M1 read all sector - sector: $sector")
        val startTime = System.currentTimeMillis()

        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }

        // 参数验证
        if (sector < 0 || sector > 15) {
            Log.e(TAG, "Invalid sector: $sector. Must be 0-15")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的扇区号: $sector，必须在0-15之间"
            result.success(response)
            return
        }

        try {
            val startBlockNo = sector * 4
            val allBlockData = HashMap<String, String>()
            var totalReadTime = 0L

            // 读取扇区内的前3个数据块 (块3是控制块，通常不读取数据)
            for (block in 0..2) {
                val blockIndex = startBlockNo + block
                val blockData = ByteArray(16)

                val blockStartTime = System.currentTimeMillis()
                Log.d(TAG, "Reading block $block (absolute: $blockIndex) in sector $sector")
                val code = currentReadCardOptV2.mifareReadBlock(blockIndex, blockData) ?: -1
                val blockEndTime = System.currentTimeMillis()
                totalReadTime += (blockEndTime - blockStartTime)

                if (code >= 0 && code <= 16) {
                    val hexStr = bytesToHexString(blockData.copyOf(code))
                    allBlockData["block$block"] = hexStr
                    Log.d(TAG, "Block $block read successfully: $hexStr")
                } else {
                    Log.e(TAG, "Failed to read block $block, error code: $code")
                    allBlockData["block$block"] = "读取失败"
                }
            }

            val endTime = System.currentTimeMillis()
            val response = HashMap<String, Any>()
            response["success"] = true
            response["data"] = allBlockData
            response["duration"] = endTime - startTime
            response["readTime"] = totalReadTime
            response["sector"] = sector
            result.success(response)

        } catch (e: Exception) {
            val endTime = System.currentTimeMillis()
            Log.e(TAG, "Error in M1 read all sector: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "批量读取异常: ${e.message}"
            response["duration"] = endTime - startTime
            result.success(response)
        }
    }

    /**
     * 批量写入M1卡扇区数据块 (参考M1Activity.writeAllSector)
     */
    fun m1WriteAllSector(sector: Int, blockData: Map<String, String>, result: MethodChannel.Result) {
        Log.d(TAG, "M1 write all sector - sector: $sector, blockData: $blockData")
        val startTime = System.currentTimeMillis()

        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }

        // 参数验证
        if (sector < 0 || sector > 15) {
            Log.e(TAG, "Invalid sector: $sector. Must be 0-15")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的扇区号: $sector，必须在0-15之间"
            result.success(response)
            return
        }

        try {
            val startBlockNo = sector * 4
            val writeResults = HashMap<String, Any>()
            var totalWriteTime = 0L
            var successCount = 0

            // 写入扇区内的前3个数据块 (块3是控制块，通常不写入)
            for (block in 0..2) {
                val blockKey = "block$block"
                val hexData = blockData[blockKey]

                if (hexData != null && hexData.isNotEmpty() && hexData != "读取失败") {
                    if (hexData.length == 32) { // 16字节 = 32个十六进制字符
                        val blockIndex = startBlockNo + block
                        val blockStartTime = System.currentTimeMillis()

                        try {
                            val dataBytes = hexStringToByteArray(hexData)
                            Log.d(TAG, "Writing block $block (absolute: $blockIndex) with data: $hexData")
                            val code = currentReadCardOptV2.mifareWriteBlock(blockIndex, dataBytes) ?: -1
                            val blockEndTime = System.currentTimeMillis()
                            totalWriteTime += (blockEndTime - blockStartTime)

                            if (code == 0) {
                                writeResults[blockKey] = "写入成功"
                                successCount++
                                Log.d(TAG, "Block $block written successfully")
                            } else {
                                val errorMsg = getM1ErrorMessage(code, "写入")
                                writeResults[blockKey] = errorMsg
                                Log.e(TAG, "Failed to write block $block, error: $errorMsg")
                            }
                        } catch (e: Exception) {
                            writeResults[blockKey] = "数据格式错误: ${e.message}"
                            Log.e(TAG, "Data format error for block $block: ${e.message}")
                        }
                    } else {
                        writeResults[blockKey] = "数据长度错误: 必须是32位十六进制字符"
                        Log.e(TAG, "Invalid data length for block $block: ${hexData.length}")
                    }
                } else {
                    writeResults[blockKey] = "跳过: 无数据或数据无效"
                    Log.d(TAG, "Skipping block $block: no data or invalid data")
                }
            }

            val endTime = System.currentTimeMillis()
            val response = HashMap<String, Any>()
            response["success"] = successCount > 0
            response["results"] = writeResults
            response["successCount"] = successCount
            response["totalBlocks"] = 3
            response["duration"] = endTime - startTime
            response["writeTime"] = totalWriteTime
            response["sector"] = sector
            result.success(response)

        } catch (e: Exception) {
            val endTime = System.currentTimeMillis()
            Log.e(TAG, "Error in M1 write all sector: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "批量写入异常: ${e.message}"
            response["duration"] = endTime - startTime
            result.success(response)
        }
    }
    
    /**
     * 验证SDK状态和常量
     */
    private fun validateSDKAndConstants(): Boolean {
        try {
            Log.d(TAG, "Card type constants - MAGNETIC: $CARD_TYPE_MAGNETIC, IC: $CARD_TYPE_IC, NFC: $CARD_TYPE_NFC")

            // 验证SDK实例
            val readCardOptV2 = EdcApplication.instance.readCardOptV2
            if (readCardOptV2 == null) {
                Log.e(TAG, "ReadCardOptV2 instance is null")
                return false
            }

            Log.d(TAG, "SDK validation passed")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "SDK validation failed: ${e.message}", e)
            return false
        }
    }

    /**
     * 验证M1卡认证参数
     */
    private fun validateM1AuthParams(sector: Int, keyType: Int, key: String): String? {
        if (sector < 0 || sector > 15) {
            return "无效的扇区号: $sector，必须在0-15之间"
        }

        if (keyType != 0 && keyType != 1) {
            return "无效的密钥类型: $keyType，必须是0(KeyA)或1(KeyB)"
        }

        if (key.replace(" ", "").length != 12) {
            return "无效的密钥长度: 必须是12位16进制字符"
        }

        return null
    }

    /**
     * 验证块参数
     */
    private fun validateBlockParams(sector: Int, block: Int): String? {
        if (sector < 0 || sector > 15) {
            return "无效的扇区号: $sector，必须在0-15之间"
        }

        if (block < 0 || block > 3) {
            return "无效的块号: $block，必须在0-3之间"
        }

        val blockIndex = sector * 4 + block
        if (blockIndex < 0 || blockIndex > 63) {
            return "无效的绝对块号: $blockIndex，必须在0-63之间"
        }

        return null
    }

    /**
     * 获取M1卡操作错误消息
     */
    private fun getM1ErrorMessage(code: Int, operation: String): String {
        return when (code) {
            -1 -> "SDK调用失败"
            -20003 -> "参数错误: 检查扇区号、密钥类型或密钥值"
            -2000 -> "卡参数错误"
            -2001 -> "没有卡片"
            -2002 -> "检测到多张卡片"
            -30001 -> "${operation}失败(未知原因)"
            else -> try {
                "${operation}失败: ${AidlErrorCodeV2.valueOf(code).msg}"
            } catch (e: Exception) {
                "${operation}失败: 未知错误代码 $code"
            }
        }
    }

    /**
     * 验证钱包操作的通用参数
     */
    private fun validateWalletParams(sector: Int, block: Int): String? {
        if (sector < 0 || sector > 15) {
            return "无效的扇区号: $sector，必须在0-15之间"
        }

        if (block < 0 || block > 2) {
            return "无效的钱包块号: $block，钱包只能使用0-2块"
        }

        val blockIndex = sector * 4 + block
        if (blockIndex < 0 || blockIndex > 63) {
            return "无效的绝对块号: $blockIndex，必须在0-63之间"
        }

        return null // 验证通过
    }

    /**
     * 获取卡类型名称
     */
    private fun getCardTypeName(cardType: Int): String {
        return when (cardType) {
            CARD_TYPE_MAGNETIC -> "MAGNETIC" // 磁条卡
            CARD_TYPE_IC -> "IC" // IC卡
            CARD_TYPE_NFC -> "NFC" // NFC卡
            CARD_TYPE_MIFARE -> "MIFARE" // MIFARE卡
            CARD_TYPE_FELICA -> "FeliCa" // FeliCa卡
            CARD_TYPE_MIFARE_PLUS -> "MIFARE Plus" // MIFARE Plus
            CARD_TYPE_MIFARE_DESFIRE -> "MIFARE DESFire" // MIFARE DESFire
            else -> "未知卡类型($cardType)"
        }
    }
    
    /**
     * 获取卡类别名称
     */
    private fun getCardCategoryName(cardCategory: Int): String {
        return when (cardCategory.toChar()) {
            'A' -> "A"
            'B' -> "B"
            else -> "未知类别($cardCategory)"
        }
    }
    
    /**
     * 16进制字符串转字节数组
     */
    private fun hexStringToByteArray(hexString: String): ByteArray {
        val hexStr = hexString.replace(" ", "").uppercase()

        // 验证输入参数
        if (hexStr.isEmpty()) {
            throw IllegalArgumentException("Hex string cannot be empty")
        }

        if (hexStr.length % 2 != 0) {
            throw IllegalArgumentException("Hex string length must be even")
        }

        // 验证是否为有效的16进制字符
        if (!hexStr.matches(Regex("[0-9A-F]*"))) {
            throw IllegalArgumentException("Invalid hex string: contains non-hex characters")
        }

        val len = hexStr.length
        val result = ByteArray(len / 2)

        for (i in 0 until len step 2) {
            val high = Character.digit(hexStr[i], 16)
            val low = Character.digit(hexStr[i + 1], 16)
            if (high == -1 || low == -1) {
                throw IllegalArgumentException("Invalid hex character at position $i")
            }
            result[i / 2] = ((high shl 4) or low).toByte()
        }

        return result
    }
    
    /**
     * 字节数组转16进制字符串
     */
    private fun bytesToHexString(bytes: ByteArray): String {
        val hexChars = CharArray(bytes.size * 2)
        
        for (i in bytes.indices) {
            val v = bytes[i].toInt() and 0xFF
            hexChars[i * 2] = "0123456789ABCDEF"[v ushr 4]
            hexChars[i * 2 + 1] = "0123456789ABCDEF"[v and 0x0F]
        }
        
        return String(hexChars)
    }
    
    /**
     * 获取初始化钱包数据 (参考M1Activity.getInitFormatData)
     * 钱包格式: 4字节值 + 4字节反码 + 4字节值 + 4字节地址信息
     */
    private fun getInitWalletData(blockIndex: Int): ByteArray {
        // 完全按照M1Activity的格式实现
        val result = byteArrayOf(
            0x00.toByte(), 0x00.toByte(), 0x00.toByte(), 0x00.toByte(),  // 初始值为0
            0xFF.toByte(), 0xFF.toByte(), 0xFF.toByte(), 0xFF.toByte(),  // 反码
            0x00.toByte(), 0x00.toByte(), 0x00.toByte(), 0x00.toByte(),  // 重复值
            0x00.toByte(), 0x00.toByte(), 0x00.toByte(), 0x00.toByte()   // 地址信息(将被覆盖)
        )

        // 设置地址信息 (块地址及其反码)
        result[12] = (blockIndex and 0xFF).toByte()
        result[13] = (blockIndex.inv() and 0xFF).toByte()  // 使用.inv()而不是手动取反
        result[14] = (blockIndex and 0xFF).toByte()
        result[15] = (blockIndex.inv() and 0xFF).toByte()

        Log.d(TAG, "Wallet init data for block $blockIndex: ${bytesToHexString(result)}")
        return result
    }
    
    /**
     * 字节数组转32位整数
     */
    private fun getInt32FromBytes(bytes: ByteArray, offset: Int, littleEndian: Boolean): Int {
        var result = 0
        
        if (littleEndian) {
            result = result or (bytes[offset].toInt() and 0xFF)
            result = result or ((bytes[offset + 1].toInt() and 0xFF) shl 8)
            result = result or ((bytes[offset + 2].toInt() and 0xFF) shl 16)
            result = result or ((bytes[offset + 3].toInt() and 0xFF) shl 24)
        } else {
            result = result or ((bytes[offset].toInt() and 0xFF) shl 24)
            result = result or ((bytes[offset + 1].toInt() and 0xFF) shl 16)
            result = result or ((bytes[offset + 2].toInt() and 0xFF) shl 8)
            result = result or (bytes[offset + 3].toInt() and 0xFF)
        }
        
        return result
    }
    
    /**
     * 32位整数转字节数组
     */
    private fun int32ToBytes(value: Int, littleEndian: Boolean): ByteArray {
        val bytes = ByteArray(4)
        
        if (littleEndian) {
            bytes[0] = (value and 0xFF).toByte()
            bytes[1] = ((value ushr 8) and 0xFF).toByte()
            bytes[2] = ((value ushr 16) and 0xFF).toByte()
            bytes[3] = ((value ushr 24) and 0xFF).toByte()
        } else {
            bytes[0] = ((value ushr 24) and 0xFF).toByte()
            bytes[1] = ((value ushr 16) and 0xFF).toByte()
            bytes[2] = ((value ushr 8) and 0xFF).toByte()
            bytes[3] = (value and 0xFF).toByte()
        }
        
        return bytes
    }
} 