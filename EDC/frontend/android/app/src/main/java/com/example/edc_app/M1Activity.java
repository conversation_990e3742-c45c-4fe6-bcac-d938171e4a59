package com.example.edc_app;

import android.os.Bundle;
import android.os.RemoteException;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Button;
import android.widget.Toast;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.sunmi.pay.hardware.aidl.AidlConstants.CardType;
import com.sunmi.pay.hardware.aidlv2.AidlErrorCodeV2;
import com.sunmi.pay.hardware.aidlv2.readcard.CheckCardCallbackV2;

import java.util.Arrays;
import java.util.Locale;

public class M1Activity extends AppCompatActivity implements View.OnClickListener {

    private static final String TAG = "M1Activity";

    private EditText mEditSector1;
    private EditText mEditKeyA1;
    private EditText mEditKeyB1;
    private EditText mEditBlock0;
    private EditText mEditBlock1;
    private EditText mEditBlock2;

    private EditText mEditSector2;
    private EditText mEditBlock;
    private EditText mEditKeyA2;
    private EditText mEditKeyB2;
    private EditText mEditCost;

    private EditText mEditSector3;
    private EditText mEditBlock3;
    private EditText mEditKeyA3;
    private EditText mEditKeyB3;

    private TextView mTvBalance;

    private int block;
    private int sector;
    private int keyType;    // 密钥类型，0表示KEY A、1表示 KEY B
    private byte[] keyBytes;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_card_m1);
        initView();
        checkCard();
    }

    private void initView() {
        // 设置标题
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("M1卡测试");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }

        mEditSector1 = findViewById(R.id.edit_sector_1);
        mEditKeyA1 = findViewById(R.id.edit_keyA_1);
        mEditKeyB1 = findViewById(R.id.edit_keyB_1);
        mEditBlock0 = findViewById(R.id.edit_block_0);
        mEditBlock1 = findViewById(R.id.edit_block_1);
        mEditBlock2 = findViewById(R.id.edit_block_2);

        mEditSector2 = findViewById(R.id.edit_sector_2);
        mEditKeyA2 = findViewById(R.id.edit_keyA_2);
        mEditKeyB2 = findViewById(R.id.edit_keyB_2);
        mEditBlock = findViewById(R.id.edit_block);
        mEditCost = findViewById(R.id.edit_cost);

        mEditSector3 = findViewById(R.id.edit_sector_3);
        mEditBlock3 = findViewById(R.id.edit_block_3);
        mEditKeyA3 = findViewById(R.id.edit_keyA_3);
        mEditKeyB3 = findViewById(R.id.edit_keyB_3);

        mTvBalance = findViewById(R.id.tv_balance);

        findViewById(R.id.mb_read).setOnClickListener(this);
        findViewById(R.id.mb_write).setOnClickListener(this);
        findViewById(R.id.mb_init).setOnClickListener(this);
        findViewById(R.id.mb_balance).setOnClickListener(this);
        findViewById(R.id.mb_add).setOnClickListener(this);
        findViewById(R.id.mb_reduce).setOnClickListener(this);
        findViewById(R.id.mb_restore).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        final int id = v.getId();
        switch (id) {
            case R.id.mb_read:
                boolean check = checkParams();
                if (check) {
                    readAllSector();
                }
                break;
            case R.id.mb_write:
                check = checkParams();
                if (check) {
                    writeAllSector();
                }
                break;
            case R.id.mb_init:
                check = checkParamsWallet();
                if (check) {
                    initWallet();
                }
                break;
            case R.id.mb_balance:
                check = checkParamsWallet();
                if (check) {
                    getBalanceWallet();
                }
                break;
            case R.id.mb_add:
                check = checkParamsWallet();
                if (check) {
                    increaseValueWallet();
                }
                break;
            case R.id.mb_reduce:
                check = checkParamsWallet();
                if (check) {
                    decreaseValueWallet();
                }
                break;
            case R.id.mb_restore:
                check = checkParamsRestore();
                if (check) {
                    restore();
                }
                break;
        }
    }

    private void checkCard() {
        try {
            Log.d(TAG, "Starting card check");
            EdcApplication.Companion.getInstance().getReadCardOptV2().checkCard(CardType.MIFARE.getValue(), mCheckCardCallback, 60);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private final CheckCardCallbackV2 mCheckCardCallback = new CheckCardCallbackV2() {

        @Override
        public void findMagCard(Bundle bundle) throws RemoteException {
            Log.d(TAG, "findMagCard");
        }

        @Override
        public void findICCardEx(Bundle info) throws RemoteException {
            Log.d(TAG, "findICCardEx:" + info.toString());
        }

        @Override
        public void findRFCard(String s) throws RemoteException {
            Log.d(TAG, "findRFCard:" + s);
        }

        @Override
        public void onError(int code, String message) throws RemoteException {
            Log.e(TAG, "Card check error: " + code + " - " + message);
        }

        @Override
        public void onErrorEx(Bundle info) throws RemoteException {
            Log.e(TAG, "Card check error ex: " + info.toString());
        }
    };

    // 显示Toast消息
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    private void showToast(int resId) {
        Toast.makeText(this, resId, Toast.LENGTH_SHORT).show();
    }

    // 参数检查方法的占位符
    private boolean checkParams() {
        // TODO: 实现参数检查逻辑
        return true;
    }

    private boolean checkParamsWallet() {
        // TODO: 实现钱包参数检查逻辑
        return true;
    }

    private boolean checkParamsRestore() {
        // TODO: 实现恢复参数检查逻辑
        return true;
    }

    // 核心功能方法的占位符
    private void readAllSector() {
        // TODO: 实现读取所有扇区逻辑
        Log.d(TAG, "readAllSector called");
    }

    private void writeAllSector() {
        // TODO: 实现写入所有扇区逻辑
        Log.d(TAG, "writeAllSector called");
    }

    private void initWallet() {
        // TODO: 实现初始化钱包逻辑
        Log.d(TAG, "initWallet called");
    }

    private void getBalanceWallet() {
        // TODO: 实现获取余额逻辑
        Log.d(TAG, "getBalanceWallet called");
    }

    private void increaseValueWallet() {
        // TODO: 实现增加金额逻辑
        Log.d(TAG, "increaseValueWallet called");
    }

    private void decreaseValueWallet() {
        // TODO: 实现减少金额逻辑
        Log.d(TAG, "decreaseValueWallet called");
    }

    private void restore() {
        // TODO: 实现恢复逻辑
        Log.d(TAG, "restore called");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        cancelCheckCard();
    }

    private void cancelCheckCard() {
        try {
            EdcApplication.Companion.getInstance().getReadCardOptV2().cardOff(CardType.MIFARE.getValue());
            EdcApplication.Companion.getInstance().getReadCardOptV2().cancelCheckCard();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}
