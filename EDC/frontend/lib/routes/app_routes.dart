import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// 常量
import '../../constants/bp_colors.dart';

// 认证服务
import '../../screens/auth/auth_service.dart';

// 页面模块
import '../../screens/home/<USER>';
import '../../screens/login/login_page.dart';

// 卡模块
import '../../screens/card/card_home_page.dart';
import '../../screens/card/nfc_page.dart';

// 打印模块
import '../../screens/printing/printing_home_page.dart';
import '../../screens/printing/receipt_reprint_page.dart';
import '../../screens/printing/printer_settings_page.dart';
import '../../screens/printing/printer_status_page.dart';
import '../../screens/printing/receipt_preview_page.dart';
// import '../../screens/printing/print_history_page.dart';

// 支付模块
import '../../screens/payment/payment_home_page.dart';
import '../../screens/payment/payment_method_selection_page.dart';
import '../../screens/payment/transaction_history_page.dart';
import '../../screens/payment/cash_payment_page.dart';
import '../../screens/payment/refund_request_page.dart';

// 会员模块
import '../../screens/member/member_registration_page.dart';

// 营销模块
import '../../screens/marketing/marketing_home_page.dart';

// 订单模块
import '../../screens/order/order_home_page.dart';
import '../../screens/order/order_detail_page.dart';
import '../../screens/order/order_search_page.dart';

// 交易模块

// 数据模型
import '../../models/order.dart';
import '../../models/dispenser_model.dart';

// 燃油交易列表
import '../../screens/fuel/fuel_transaction_list_page.dart';
import '../../screens/fuel/nozzle_authorization_page.dart';
import '../../screens/fuel/nozzle_transaction_selection_page.dart';

// 设置模块
import '../../screens/settings/settings_home_page.dart';
import '../../screens/settings/timezone_settings_page.dart';

// 班次模块
import '../../screens/shift/shift_home_page.dart';
import '../../screens/shift/shift_history_page.dart';
// import '../../screens/shift/shift_report_page.dart'; // 已删除

import '../../models/payment_transaction_data.dart';

// 创建 GoRouter Provider
final Provider<GoRouter> goRouterProvider =
    Provider<GoRouter>((ProviderRef<GoRouter> ref) {
  // 监听认证服务的变化
  final AuthService authService = ref.watch(authServiceProvider);

  return GoRouter(
    // 初始位置：让 redirect 处理
    initialLocation: '/',
    redirect: (BuildContext context, GoRouterState state) {
      // 使用认证服务的状态
      final bool isLoggedIn = authService.isLoggedInSync;
      final String location = state.uri.toString();
      debugPrint(
          '🔍 RouterProvider Redirect Check - URL: $location, isLoggedIn: $isLoggedIn');

      // 检查是否从登录成功按钮导航 (使用 extra)
      bool fromLogin = false;
      // Only attempt to access 'fromLogin' if state.extra is actually a Map
      if (state.extra is Map<String, dynamic>) {
        final Map<String, dynamic> extraMap =
            state.extra as Map<String, dynamic>;
        fromLogin = (extraMap['fromLogin'] as bool?) ?? false;
      }

      if (fromLogin) {
        debugPrint(
            '✅ Detected navigation from login button (extra), allowing access to: $location');
        return null; // 直接放行
      }

      // 允许访问登录页
      if (location == '/login') {
        if (isLoggedIn) {
          debugPrint('✅ User is logged in, redirecting from /login to /');
          return '/';
        }
        debugPrint('✅ Allowing access to /login page');
        return null;
      }

      // 如果未登录，重定向到登录页
      if (!isLoggedIn) {
        debugPrint('❌ User not logged in, redirecting to /login');
        return '/login';
      }

      // 用户已登录，允许访问
      debugPrint('✅ User logged in, allowing access to: $location');
      return null;
    },
    refreshListenable: authService, // 监听认证服务的变化
    debugLogDiagnostics: true,
    routes: <RouteBase>[
      // 登录页
      GoRoute(
        path: '/login',
        builder: (BuildContext context, GoRouterState state) =>
            const LoginPage(),
      ),

      // 主页
      GoRoute(
        path: '/',
        builder: (BuildContext context, GoRouterState state) =>
            const MainNavigationPage(),
        routes: <RouteBase>[
          GoRoute(
            path: 'fuel-transactions',
            builder: (BuildContext context, GoRouterState state) =>
                const FuelTransactionListPage(),
          ),
          GoRoute(
            path: 'nozzle-authorization',
            builder: (BuildContext context, GoRouterState state) {
              // Extract data from extra
              final Map<String, dynamic>? data =
                  state.extra as Map<String, dynamic>?;
              final Nozzle? selectedNozzle = data?['selectedNozzle'] as Nozzle?;

              // 如果没有传入选中的nozzle，返回错误页面
              if (selectedNozzle == null) {
                return Scaffold(
                  appBar: AppBar(title: const Text('Error')),
                  body: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        const Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Nozzle not found. Please select again.',
                          style: TextStyle(fontSize: 18),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('Go Back'),
                        ),
                      ],
                    ),
                  ),
                );
              }

              return NozzleAuthorizationPage(
                selectedNozzle: selectedNozzle,
              );
            },
          ),
        ],
      ),

      // 卡模块
      GoRoute(
        path: '/card',
        builder: (BuildContext context, GoRouterState state) =>
            const CardHomePage(),
        routes: <RouteBase>[
          GoRoute(
            path: 'nfc',
            builder: (BuildContext context, GoRouterState state) =>
                const NFCPage(),
          ),
        ],
      ),

      // 打印模块
      GoRoute(
        path: '/printing',
        builder: (BuildContext context, GoRouterState state) =>
            const PrintingHomePage(),
        routes: <RouteBase>[
          GoRoute(
            path: 'receipt_reprint',
            builder: (BuildContext context, GoRouterState state) =>
                const ReceiptReprintPage(),
          ),
          GoRoute(
            path: 'settings',
            builder: (BuildContext context, GoRouterState state) =>
                const PrinterSettingsPage(),
          ),
          GoRoute(
            path: 'status',
            builder: (BuildContext context, GoRouterState state) =>
                const PrinterStatusPage(),
          ),
          GoRoute(
            path: 'preview',
            builder: (BuildContext context, GoRouterState state) {
              final String orderId = state.extra as String? ?? "";
              return ReceiptPreviewPage(orderId: orderId);
            },
          ),
        ],
      ),

      // 支付模块
      GoRoute(
        path: '/payment',
        builder: (BuildContext context, GoRouterState state) {
          final Object? extra = state.extra;
          PaymentTransactionData? paymentData;

          if (extra is PaymentTransactionData) {
            paymentData = extra;
          } else if (extra is Map<String, dynamic>) {
            // 兼容旧版本的Map数据
            paymentData = PaymentTransactionData.fromLegacyMap(extra);
          }

          return PaymentHomePage(paymentData: paymentData);
        },
        routes: <RouteBase>[
          GoRoute(
            path: 'methods',
            builder: (BuildContext context, GoRouterState state) {
              final PaymentTransactionData? paymentData =
                  state.extra as PaymentTransactionData?;
              return PaymentMethodSelectionPage(paymentData: paymentData);
            },
          ),
          GoRoute(
            path: 'history',
            builder: (BuildContext context, GoRouterState state) =>
                const TransactionHistoryPage(),
          ),
          GoRoute(
              path: 'cash',
              builder: (BuildContext context, GoRouterState state) {
                final PaymentTransactionData? paymentData =
                    state.extra as PaymentTransactionData?;
                return CashPaymentPage(paymentData: paymentData);
              }),
          GoRoute(
            path: 'refund',
            builder: (BuildContext context, GoRouterState state) =>
                const RefundRequestPage(),
          ),
        ],
      ),

      // 会员模块
      GoRoute(
        path: '/member',
        builder: (BuildContext context, GoRouterState state) => Scaffold(
          appBar: AppBar(title: const Text('会员管理')),
          body: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Icon(Icons.person, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text('会员功能正在开发中...',
                    style: TextStyle(fontSize: 18, color: Colors.grey)),
              ],
            ),
          ),
        ),
        routes: <RouteBase>[
          GoRoute(
            path: 'register',
            builder: (BuildContext context, GoRouterState state) =>
                const MemberRegistrationPage(),
          ),
        ],
      ),

      // 营销模块
      GoRoute(
        path: '/marketing',
        builder: (BuildContext context, GoRouterState state) =>
            const MarketingHomePage(),
        routes: const <RouteBase>[
          // 营销相关路由将在此处添加
        ],
      ),

      // 订单模块
      GoRoute(
        path: '/order',
        builder: (BuildContext context, GoRouterState state) =>
            const OrderHomePage(),
        routes: <RouteBase>[
          GoRoute(
            path: 'detail',
            builder: (BuildContext context, GoRouterState state) {
              // final String orderId = state.extra as String? ?? ''; // OLD way
              // return OrderDetailPage(orderId: orderId);           // OLD way

              // NEW way: Expect int from extra
              final String? orderId =
                  state.extra as String?; // Try casting extra to int?

              if (orderId == null) {
                // Handle missing/invalid ID passed via extra
                // Log error for debugging
                debugPrint(
                    'Error navigating to order detail: Missing or invalid integer ID in extra.');
                // Return an error view or redirect
                return Scaffold(
                  appBar: AppBar(title: const Text('错误')),
                  body: const Center(child: Text('无法加载订单详情，订单ID无效或丢失。')),
                );
              }
              // Pass the valid integer ID to the page
              return OrderDetailPage(orderId: orderId);
            },
          ),
          GoRoute(
            path: 'search',
            builder: (BuildContext context, GoRouterState state) {
              // Try to cast state.extra to OrderQueryCondition?
              final OrderQueryCondition? initialCondition =
                  state.extra as OrderQueryCondition?;

              // Pass the received condition (or a default one if null) to the page
              return OrderSearchPage(
                initialCondition: initialCondition ??
                    OrderQueryCondition(
                        // Provide default values if needed, e.g., last 7 days
                        // startDate: DateTime.now().subtract(const Duration(days: 7)),
                        // endDate: DateTime.now(),
                        ),
              );
            },
          ),
        ],
      ),

      // 设置模块
      GoRoute(
        path: '/settings',
        builder: (BuildContext context, GoRouterState state) =>
            const SettingsHomePage(),
        routes: <RouteBase>[
          GoRoute(
            path: 'timezone',
            builder: (BuildContext context, GoRouterState state) =>
                const TimezoneSettingsPage(),
          ),
        ],
      ),

      // 班次模块
      GoRoute(
        path: '/shift',
        builder: (BuildContext context, GoRouterState state) =>
            const ShiftHomePage(),
        routes: <RouteBase>[
          GoRoute(
            path: 'history',
            builder: (BuildContext context, GoRouterState state) =>
                const ShiftHistoryPage(),
          ),
          GoRoute(
            path: 'report',
            builder: (BuildContext context, GoRouterState state) {
              final Map<String, dynamic>? data =
                  state.extra as Map<String, dynamic>?;
              final int? shiftId = data?['shiftId'] as int?;
              final String? shiftNumber = data?['shiftNumber'] as String?;

              if (shiftId == null || shiftNumber == null) {
                return Scaffold(
                  appBar: AppBar(title: const Text('Error')),
                  body: const Center(
                    child: Text('Invalid shift data provided'),
                  ),
                );
              }

              // ShiftReportPage已删除，返回占位页面
              return Scaffold(
                appBar: AppBar(
                  title: const Text('Shift Report'),
                  backgroundColor: BPColors.primary,
                  foregroundColor: Colors.white,
                ),
                body: const Center(
                  child: Text(
                    'Shift report feature is under development',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
              );
            },
          ),
        ],
      ),

      // 燃油模块
      GoRoute(
        path: '/fuel',
        redirect: (BuildContext context, GoRouterState state) =>
            '/fuel-transactions', // 默认重定向到交易列表
        routes: <RouteBase>[
          GoRoute(
            path: 'nozzle-transactions',
            builder: (BuildContext context, GoRouterState state) {
              final Map<String, dynamic>? pageData =
                  state.extra as Map<String, dynamic>?;

              if (pageData == null) {
                return Scaffold(
                  appBar: AppBar(title: const Text('Error')),
                  body: const Center(
                    child: Text('No transaction data provided'),
                  ),
                );
              }

              return NozzleTransactionSelectionPage(pageData: pageData);
            },
          ),
        ],
      ),
    ],
  );
});
